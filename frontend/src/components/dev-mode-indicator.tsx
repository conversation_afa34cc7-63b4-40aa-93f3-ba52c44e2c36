'use client';

import React from 'react';
import { isDevelopmentMode, getBillingStatusMessage } from '@/lib/billing';

export function DevModeIndicator() {
  if (!isDevelopmentMode()) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-4 py-2 rounded-lg shadow-lg border border-green-400">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
          <span className="text-sm font-medium">
            🚀 Dev Mode: Unlimited Access
          </span>
        </div>
        <div className="text-xs opacity-90 mt-1">
          All premium features enabled
        </div>
      </div>
    </div>
  );
}

export default DevModeIndicator;
