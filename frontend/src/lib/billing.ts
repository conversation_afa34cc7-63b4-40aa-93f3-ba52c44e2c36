/**
 * Enhanced Billing Helper - Development Mode
 * Provides unlimited access and premium feature simulation for localhost development
 * 
 * This module transforms the development experience to match production premium tiers
 */

// Environment detection
const isDevelopmentMode = (): boolean => {
  return process.env.NEXT_PUBLIC_ENV_MODE === 'local' || 
         process.env.NODE_ENV === 'development';
};

const isBillingDisabled = (): boolean => {
  return process.env.NEXT_PUBLIC_BILLING_ENABLED === 'false' || isDevelopmentMode();
};

// Backend URL configuration
export const getBackendUrl = (): string => {
  return process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000/api';
};

// Core billing status checker - returns unlimited access in development
export function shouldEnforceBilling(): boolean {
  if (isDevelopmentMode()) {
    console.log('🚀 Development Mode: Billing enforcement disabled - unlimited access');
    return false;
  }
  
  return process.env.NEXT_PUBLIC_BILLING_ENABLED === 'true';
}

// Enhanced billing configuration with development mode features
export function getBillingConfig() {
  const devMode = isDevelopmentMode();
  
  return {
    enabled: !devMode && shouldEnforceBilling(),
    backendUrl: getBackendUrl(),
    mode: process.env.NEXT_PUBLIC_ENV_MODE || 'production',
    developmentMode: devMode,
    unlimitedAccess: devMode,
    premiumFeaturesEnabled: devMode,
    customAgentsEnabled: devMode,
    allModelsAvailable: devMode
  };
}

// React hook for billing status with development mode support
export function useBillingEnabled(): boolean {
  const config = getBillingConfig();
  
  if (config.developmentMode) {
    console.log('🔓 Development Mode: All billing restrictions bypassed');
    return false;
  }
  
  return config.enabled;
}

// Premium feature access helper - always true in development
export function hasFeatureAccess(feature: string): boolean {
  if (isDevelopmentMode()) {
    console.log(`✅ Development Mode: ${feature} feature access granted`);
    return true;
  }
  
  // In production, this would check actual subscription
  return shouldEnforceBilling();
}

// Model access helper - all models available in development
export function hasModelAccess(modelName: string): boolean {
  if (isDevelopmentMode()) {
    console.log(`🤖 Development Mode: ${modelName} model access granted`);
    return true;
  }

  // Production logic would check subscription tier
  return shouldEnforceBilling();
}

// Model validation helper - always valid in development
export function isValidModel(modelId: string): boolean {
  if (isDevelopmentMode()) {
    console.log(`✅ Development Mode: ${modelId} model is valid`);
    return true;
  }

  // Production logic would validate against available models
  return true;
}

// Usage limits helper - unlimited in development
export function checkUsageLimit(feature: string, currentUsage: number = 0): {
  allowed: boolean;
  limit: number;
  used: number;
  remaining: number;
  unlimited: boolean;
} {
  if (isDevelopmentMode()) {
    return {
      allowed: true,
      limit: Infinity,
      used: currentUsage,
      remaining: Infinity,
      unlimited: true
    };
  }
  
  // Production limits would be calculated here
  return {
    allowed: true,
    limit: 100,
    used: currentUsage,
    remaining: Math.max(0, 100 - currentUsage),
    unlimited: false
  };
}

// Subscription tier simulation for development
export function getSimulatedSubscriptionTier(): {
  tier: string;
  name: string;
  features: string[];
  unlimited: boolean;
} {
  if (isDevelopmentMode()) {
    return {
      tier: 'development',
      name: 'Development Pro (Unlimited)',
      features: [
        'Unlimited AI model access',
        'Custom agents & workflows',
        'Advanced integrations', 
        'Premium templates',
        'Unlimited usage & storage',
        'All premium features enabled'
      ],
      unlimited: true
    };
  }
  
  return {
    tier: 'free',
    name: 'Free Tier',
    features: ['Basic features'],
    unlimited: false
  };
}

// Development mode status messages
export function getBillingStatusMessage(): string {
  if (isDevelopmentMode()) {
    return '🚀 Running in Development Mode - All premium features enabled, unlimited usage';
  }
  
  return shouldEnforceBilling() ? 'Billing is active' : 'Billing is disabled';
}

// Custom agents availability - always true in development
export function areCustomAgentsEnabled(): boolean {
  if (isDevelopmentMode()) {
    console.log('🤖 Development Mode: Custom agents fully enabled');
    return true;
  }
  
  return shouldEnforceBilling();
}

// Advanced workflows availability - always true in development  
export function areWorkflowsEnabled(): boolean {
  if (isDevelopmentMode()) {
    console.log('⚡ Development Mode: Advanced workflows fully enabled');
    return true;
  }
  
  return shouldEnforceBilling();
}

// Conditional rendering helper for development
export function withBilling<T>(component: T, fallback?: T): T | undefined {
  if (isDevelopmentMode()) {
    return fallback; // In dev mode, show the non-billing version
  }
  
  return shouldEnforceBilling() ? component : fallback;
}

// Development mode feature detector
export { isBillingDisabled, isDevelopmentMode };

// Legacy support
export const isLocalMode = isDevelopmentMode;
