DROP FUNCTION IF EXISTS get_available_credits(UUID);
DROP FUNCTION IF EXISTS add_credits(U<PERSON><PERSON>, <PERSON>CIMA<PERSON>, TEXT, B<PERSON>OLEAN, TIMESTAMPTZ);
DROP FUNCTION IF EXISTS use_credits(U<PERSON><PERSON>, <PERSON>CIMA<PERSON>, TEX<PERSON>, UUI<PERSON>, UUID);
DROP FUNCTION IF EXISTS reset_expiring_credits(U<PERSON><PERSON>, <PERSON>CIMA<PERSON>, TEXT);

DROP FUNCTION IF EXISTS add_credits(UUID, <PERSON>CIMA<PERSON>, TEXT);
DROP FUNCTION IF EXISTS use_credits(U<PERSON><PERSON>, <PERSON>CIMAL, TEXT, UUID, UUID);

DROP FUNCTION IF EXISTS temp_calculate_credit_split(UUID);
DROP FUNCTION IF EXISTS calculate_existing_credit_split(UUID);

COMMENT ON TABLE credit_accounts IS 'Credit management is now handled in Python code (billing.credit_manager) instead of database functions for better maintainability'; 