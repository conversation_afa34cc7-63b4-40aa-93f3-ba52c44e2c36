# Generated by <PERSON><PERSON> install script for 'docker' setup

ENV_MODE=local
SUPABASE_URL=https://oybiznvojgdxvimqdgto.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im95Yml6bnZvamdkeHZpbXFkZ3RvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTU3Nzg0MjcsImV4cCI6MjA3MTM1NDQyN30.G4_JeSB2rmE7UbEnZEIZPq6Z_ZJkw0-nr7tCPOJk9C0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im95Yml6bnZvamdkeHZpbXFkZ3RvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NTc3ODQyNywiZXhwIjoyMDcxMzU0NDI3fQ.LHUm3DMnxTHXpDwXX-D3MLlTalw3mbqi7xtYoQk7NOg
SUPABASE_JWT_SECRET=UeRImvrQm1w5G0UJG+4C2+KEi/qA5nZdj1NiNO+JXQig3pwUUEVEG/40wGkaTZSbV0CoZdRwR/JZ2d+NUU+OVg==
REDIS_HOST=redis
REDIS_PORT=6379
OPENAI_API_KEY=********************************************************************************************************************************************************************
ANTHROPIC_API_KEY=************************************************************************************************************
OPENROUTER_API_KEY=
MORPH_API_KEY=sk-Qp-o-X2JXlIcTovCpwDK4fWiVdpiB7xy6cUF4h3B933UIy5Y
GEMINI_API_KEY=
TAVILY_API_KEY=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
FIRECRAWL_API_KEY=fc-e51d01b737e4432a85e0d935ea5173a1
FIRECRAWL_URL=https://api.firecrawl.dev
RAPID_API_KEY=d8310db243msh4c4bce704b2e742p135944jsnba66febaf9e7
WEBHOOK_BASE_URL=http://localhost:3000
TRIGGER_WEBHOOK_SECRET=5d7ebaa5127934a7c90f05f9d19637e92ee55b551832475f677ed198d8d5fcae
MCP_CREDENTIAL_ENCRYPTION_KEY=x8o7oZ5MbEJV2Qj8TNwEu0n4uagmid7/fliSewzXlM4=
COMPOSIO_API_KEY=pr_rctrz6RqI4aM
COMPOSIO_WEBHOOK_SECRET=
DAYTONA_API_KEY=dtn_fee009039ae97c01f9dc1207c80e01338252b040a117ffa50cb84fcc82e66878
DAYTONA_SERVER_URL=https://app.daytona.io/api
DAYTONA_TARGET=us
KORTIX_ADMIN_API_KEY=b584cd4cc79a39b3f69d5467998395482edb4ed787488f2a909dafb8497241b8
ENCRYPTION_KEY=rhAAR6AzTXRNPM/NMqPNMH70V0zwcgxFxClxPJW1zl8=
NEXT_PUBLIC_URL=http://localhost:3000
