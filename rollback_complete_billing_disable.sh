#!/bin/bash

# Comprehensive Rollback Script - Complete Billing Disable
# Generated by disable_billing_complete.sh on Wed Sep 10 13:26:37 IST 2025

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
success() { echo -e "${GREEN}✅ $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }

echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║                                                              ║${NC}"
echo -e "${PURPLE}║               🔄 COMPREHENSIVE ROLLBACK 🔄                  ║${NC}"
echo -e "${PURPLE}║                                                              ║${NC}"
echo -e "${PURPLE}║         Restoring original billing configuration            ║${NC}"
echo -e "${PURPLE}║                                                              ║${NC}"
echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

info "🔄 Rolling back comprehensive billing disable changes..."

# <PERSON>ore backed up files
if [ -d "complete_billing_disable_backup_20250910_132636" ]; then
    echo "📁 Restoring files from backup: complete_billing_disable_backup_20250910_132636"
    
    [ -f "complete_billing_disable_backup_20250910_132636/.env.backend.backup" ] && cp "complete_billing_disable_backup_20250910_132636/.env.backend.backup" "backend/.env" && success "✅ Restored backend .env"
    [ -f "complete_billing_disable_backup_20250910_132636/.env.local.backup" ] && cp "complete_billing_disable_backup_20250910_132636/.env.local.backup" "frontend/.env.local" && success "✅ Restored frontend .env.local"
    [ -f "complete_billing_disable_backup_20250910_132636/billing.ts.backup" ] && cp "complete_billing_disable_backup_20250910_132636/billing.ts.backup" "frontend/src/lib/billing.ts" && success "✅ Restored billing helper"
    
    echo "🗑️  Cleaning up backup directory: complete_billing_disable_backup_20250910_132636"
    rm -rf "complete_billing_disable_backup_20250910_132636"
else
    warning "⚠️  Backup directory complete_billing_disable_backup_20250910_132636 not found"
fi

# Remove created development files
[ -f "frontend/src/lib/billing.ts" ] && [ ! -f "complete_billing_disable_backup_20250910_132636/billing.ts.backup" ] && rm -f "frontend/src/lib/billing.ts" && success "🗑️  Removed development billing helper"
[ -f "frontend/src/components/dev-mode-indicator.tsx" ] && rm -f "frontend/src/components/dev-mode-indicator.tsx" && success "🗑️  Removed dev mode indicator"

# Clean up .bak files created by sed
find . -name "*.bak" -delete 2>/dev/null && success "🧹 Cleaned up .bak files"

success "✅ Rollback completed successfully!"
echo ""
info "📋 What was restored:"
info "   • Backend environment variables (ENV_MODE, BILLING_ENABLED)"
info "   • Frontend environment variables (NEXT_PUBLIC_ENV_MODE, NEXT_PUBLIC_BILLING_ENABLED)"
info "   • Original billing helper library"
echo ""
warning "⚠️  Note: Feature flags in Redis are NOT automatically rolled back"
warning "    Use: ./manage_feature_flags.sh disable-all to disable feature flags"
echo ""
info "🚀 Next steps after rollback:"
info "   1. Restart Docker containers: docker compose down && docker compose up --build"
info "   2. Verify billing is re-enabled in the application"
info "   3. Check that premium features now require subscription"

# Self-destruct
rm -f "$0"
